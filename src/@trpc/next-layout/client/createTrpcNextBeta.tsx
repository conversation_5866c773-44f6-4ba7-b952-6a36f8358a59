/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-return */

"use client";

import { useMemo, useState } from "react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import type { CreateTRPCClientOptions } from "@trpc/client";
import { createTRPCReact } from "@trpc/react-query";
import type { AnyRouter } from "@trpc/server";

export type WithTRPCConfig<TRouter extends AnyRouter> =
  CreateTRPCClientOptions<TRouter> & {
    queryClientConfig?: {
      defaultOptions?: {
        queries?: any;
        mutations?: any;
      };
    };
  };

type WithTRPCOptions<TRouter extends AnyRouter> = WithTRPCConfig<TRouter>;

/**
 * @internal
 */
export interface CreateTRPCNextBase<TRouter extends AnyRouter> {
  useUtils(): any;
  Provider: ({ children }: { children: React.ReactNode }) => JSX.Element;
}

/**
 * @internal
 */
export type CreateTRPCNext<
  TRouter extends AnyRouter,
  TFlags
> = CreateTRPCNextBase<TRouter> & ReturnType<typeof createTRPCReact<TRouter>>;

export function createTRPCNextBeta<TRouter extends AnyRouter, TFlags = null>(
  opts: WithTRPCOptions<TRouter>
): CreateTRPCNext<TRouter, TFlags> {
  const trpc = createTRPCReact<TRouter>();

  const TRPCProvider = ({ children }: { children: React.ReactNode }) => {
    const [prepassProps] = useState(() => {
      const queryClient = new QueryClient(opts.queryClientConfig || {
        defaultOptions: {
          queries: {
            refetchOnWindowFocus: false,
            staleTime: 5 * 60 * 1000, // 5 minutes
            cacheTime: 10 * 60 * 1000, // 10 minutes
            retry: 3,
            retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
          },
          mutations: {
            retry: 1,
          },
        },
      });
      const trpcClient = trpc.createClient(opts);
      return {
        queryClient,
        trpcClient,
      };
    });

    const { queryClient, trpcClient } = prepassProps;

    return (
      <trpc.Provider client={trpcClient} queryClient={queryClient}>
        <QueryClientProvider client={queryClient}>
          {children}
        </QueryClientProvider>
      </trpc.Provider>
    );
  };

  // Create a proxy that combines the tRPC hooks with our custom Provider
  return new Proxy(trpc as any, {
    get(target, prop) {
      if (prop === "Provider") {
        return TRPCProvider;
      }
      if (prop === "useUtils") {
        return target.useUtils;
      }
      return target[prop as keyof typeof target];
    },
  }) as CreateTRPCNext<TRouter, TFlags>;
}

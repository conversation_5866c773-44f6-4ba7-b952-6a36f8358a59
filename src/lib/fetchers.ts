import { prisma } from "@/server/db"
import type { Show } from "@/types"
import type { MEDIA_TYPE } from "@prisma/client"

export async function getShows(mediaType: MEDIA_TYPE) {
  // Get shows by different categories from database
  const [trending, topRated, netflix, action, comedy, horror, romance, docs] = await Promise.all([
    // Trending shows
    prisma.show.findMany({
      where: {
        mediaType: mediaType as MEDIA_TYPE,
        isTrending: true,
      },
      take: 20,
      orderBy: {
        popularity: "desc",
      },
    }),
    // Top rated shows
    prisma.show.findMany({
      where: {
        mediaType: mediaType as MEDIA_TYPE,
        isTopRated: true,
      },
      take: 20,
      orderBy: {
        voteAverage: "desc",
      },
    }),
    // Netflix shows
    prisma.show.findMany({
      where: {
        mediaType: mediaType as MEDIA_TYPE,
        isNetflix: true,
      },
      take: 20,
      orderBy: {
        popularity: "desc",
      },
    }),
    // Action shows (genre id 28)
    prisma.show.findMany({
      where: {
        mediaType: mediaType as ME<PERSON>A_TYPE,
        genres: {
          some: {
            genreId: 28,
          },
        },
      },
      take: 20,
      orderBy: {
        popularity: "desc",
      },
    }),
    // Comedy shows (genre id 35)
    prisma.show.findMany({
      where: {
        mediaType: mediaType as MEDIA_TYPE,
        genres: {
          some: {
            genreId: 35,
          },
        },
      },
      take: 20,
      orderBy: {
        popularity: "desc",
      },
    }),
    // Horror shows (genre id 27)
    prisma.show.findMany({
      where: {
        mediaType: mediaType as MEDIA_TYPE,
        genres: {
          some: {
            genreId: 27,
          },
        },
      },
      take: 20,
      orderBy: {
        popularity: "desc",
      },
    }),
    // Romance shows (genre id 10749)
    prisma.show.findMany({
      where: {
        mediaType: mediaType as MEDIA_TYPE,
        genres: {
          some: {
            genreId: 10749,
          },
        },
      },
      take: 20,
      orderBy: {
        popularity: "desc",
      },
    }),
    // Documentary shows (genre id 99)
    prisma.show.findMany({
      where: {
        mediaType: mediaType as MEDIA_TYPE,
        genres: {
          some: {
            genreId: 99,
          },
        },
      },
      take: 20,
      orderBy: {
        popularity: "desc",
      },
    }),
  ])

  // Transform the data to match the expected format
  const transformShow = (show: any): Show => ({
    showId: show.id,
    id: show.tmdbId || 0,
    title: show.title,
    name: show.name,
    original_title: show.originalTitle,
    poster_path: show.posterPath,
    backdrop_path: show.backdropPath,
    overview: show.overview,
    original_language: show.originalLanguage,
    media_type: show.mediaType,
    status: show.status,
    tagline: show.tagline,
    budget: show.budget,
    homepage: show.homepage,
    imdb_id: show.imdbId,
    popularity: show.popularity,
    vote_average: show.voteAverage,
    vote_count: show.voteCount,
    release_date: show.releaseDate,
    first_air_date: show.firstAirDate,
    last_air_date: show.lastAirDate,
    number_of_seasons: show.numberOfSeasons,
    number_of_episodes: show.numberOfEpisodes,
    revenue: show.revenue,
    runtime: show.runtime,
    adult: show.adult,
    video: show.video,
  })

  return {
    trending: trending.map(transformShow),
    topRated: topRated.map(transformShow),
    netflix: netflix.map(transformShow),
    action: action.map(transformShow),
    comedy: comedy.map(transformShow),
    horror: horror.map(transformShow),
    romance: romance.map(transformShow),
    docs: docs.map(transformShow),
  }
}

export async function getNewAndPopularShows() {
  const [popularTvs, popularMovies, trendingTvs, trendingMovies] = await Promise.all([
    // Popular TV shows
    prisma.show.findMany({
      where: {
        mediaType: "tv",
      },
      take: 20,
      orderBy: {
        popularity: "desc",
      },
    }),
    // Popular movies
    prisma.show.findMany({
      where: {
        mediaType: "movie",
      },
      take: 20,
      orderBy: {
        popularity: "desc",
      },
    }),
    // Trending TV shows
    prisma.show.findMany({
      where: {
        mediaType: "tv",
        isTrending: true,
      },
      take: 20,
      orderBy: {
        popularity: "desc",
      },
    }),
    // Trending movies
    prisma.show.findMany({
      where: {
        mediaType: "movie",
        isTrending: true,
      },
      take: 20,
      orderBy: {
        popularity: "desc",
      },
    }),
  ])

  // Transform the data to match the expected format
  const transformShow = (show: any): Show => ({
    showId: show.id,
    id: show.tmdbId || 0,
    title: show.title,
    name: show.name,
    original_title: show.originalTitle,
    poster_path: show.posterPath,
    backdrop_path: show.backdropPath,
    overview: show.overview,
    original_language: show.originalLanguage,
    media_type: show.mediaType,
    status: show.status,
    tagline: show.tagline,
    budget: show.budget,
    homepage: show.homepage,
    imdb_id: show.imdbId,
    popularity: show.popularity,
    vote_average: show.voteAverage,
    vote_count: show.voteCount,
    release_date: show.releaseDate,
    first_air_date: show.firstAirDate,
    last_air_date: show.lastAirDate,
    number_of_seasons: show.numberOfSeasons,
    number_of_episodes: show.numberOfEpisodes,
    revenue: show.revenue,
    runtime: show.runtime,
    adult: show.adult,
    video: show.video,
  })

  return {
    popularTvs: popularTvs.map(transformShow),
    popularMovies: popularMovies.map(transformShow),
    trendingTvs: trendingTvs.map(transformShow),
    trendingMovies: trendingMovies.map(transformShow),
  }
}

export async function searchShows(query: string) {
  const shows = await prisma.show.findMany({
    where: {
      OR: [
        {
          title: {
            contains: query,
            mode: "insensitive",
          },
        },
        {
          name: {
            contains: query,
            mode: "insensitive",
          },
        },
        {
          overview: {
            contains: query,
            mode: "insensitive",
          },
        },
      ],
    },
    take: 50,
    orderBy: {
      popularity: "desc",
    },
  })

  // Transform the data to match the expected format
  const transformShow = (show: any): Show => ({
    showId: show.id,
    id: show.tmdbId || 0,
    title: show.title,
    name: show.name,
    original_title: show.originalTitle,
    poster_path: show.posterPath,
    backdrop_path: show.backdropPath,
    overview: show.overview,
    original_language: show.originalLanguage,
    media_type: show.mediaType,
    status: show.status,
    tagline: show.tagline,
    budget: show.budget,
    homepage: show.homepage,
    imdb_id: show.imdbId,
    popularity: show.popularity,
    vote_average: show.voteAverage,
    vote_count: show.voteCount,
    release_date: show.releaseDate,
    first_air_date: show.firstAirDate,
    last_air_date: show.lastAirDate,
    number_of_seasons: show.numberOfSeasons,
    number_of_episodes: show.numberOfEpisodes,
    revenue: show.revenue,
    runtime: show.runtime,
    adult: show.adult,
    video: show.video,
  })

  return {
    results: shows.map(transformShow),
  }
}

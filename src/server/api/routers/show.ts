import { z } from "zod"
import { createTRPCRouter, publicProcedure } from "@/server/api/trpc"
import { prisma } from "@/server/db"
import type { MEDIA_TYPE } from "@prisma/client"

export const showRouter = createTRPCRouter({
  // Get shows by category and media type
  getShows: publicProcedure
    .input(z.object({
      mediaType: z.enum(["movie", "tv"]),
    }))
    .query(async ({ input }) => {
      const { mediaType } = input

      // Get shows by different categories
      const [trending, topRated, netflix, action, comedy, horror, romance, docs] = await Promise.all([
        // Trending shows
        prisma.show.findMany({
          where: {
            mediaType: mediaType as MEDIA_TYPE,
            isTrending: true,
          },
          include: {
            genres: {
              include: {
                genre: true,
              },
            },
            videos: {
              where: {
                type: "Trailer",
              },
              take: 1,
            },
          },
          take: 20,
          orderBy: {
            popularity: "desc",
          },
        }),
        // Top rated shows
        prisma.show.findMany({
          where: {
            mediaType: mediaType as MEDIA_TYPE,
            isTopRated: true,
          },
          include: {
            genres: {
              include: {
                genre: true,
              },
            },
            videos: {
              where: {
                type: "Trailer",
              },
              take: 1,
            },
          },
          take: 20,
          orderBy: {
            voteAverage: "desc",
          },
        }),
        // Netflix shows
        prisma.show.findMany({
          where: {
            mediaType: mediaType as MEDIA_TYPE,
            isNetflix: true,
          },
          include: {
            genres: {
              include: {
                genre: true,
              },
            },
            videos: {
              where: {
                type: "Trailer",
              },
              take: 1,
            },
          },
          take: 20,
          orderBy: {
            popularity: "desc",
          },
        }),
        // Action shows (genre id 28)
        prisma.show.findMany({
          where: {
            mediaType: mediaType as MEDIA_TYPE,
            genres: {
              some: {
                genreId: 28,
              },
            },
          },
          include: {
            genres: {
              include: {
                genre: true,
              },
            },
            videos: {
              where: {
                type: "Trailer",
              },
              take: 1,
            },
          },
          take: 20,
          orderBy: {
            popularity: "desc",
          },
        }),
        // Comedy shows (genre id 35)
        prisma.show.findMany({
          where: {
            mediaType: mediaType as MEDIA_TYPE,
            genres: {
              some: {
                genreId: 35,
              },
            },
          },
          include: {
            genres: {
              include: {
                genre: true,
              },
            },
            videos: {
              where: {
                type: "Trailer",
              },
              take: 1,
            },
          },
          take: 20,
          orderBy: {
            popularity: "desc",
          },
        }),
        // Horror shows (genre id 27)
        prisma.show.findMany({
          where: {
            mediaType: mediaType as MEDIA_TYPE,
            genres: {
              some: {
                genreId: 27,
              },
            },
          },
          include: {
            genres: {
              include: {
                genre: true,
              },
            },
            videos: {
              where: {
                type: "Trailer",
              },
              take: 1,
            },
          },
          take: 20,
          orderBy: {
            popularity: "desc",
          },
        }),
        // Romance shows (genre id 10749)
        prisma.show.findMany({
          where: {
            mediaType: mediaType as MEDIA_TYPE,
            genres: {
              some: {
                genreId: 10749,
              },
            },
          },
          include: {
            genres: {
              include: {
                genre: true,
              },
            },
            videos: {
              where: {
                type: "Trailer",
              },
              take: 1,
            },
          },
          take: 20,
          orderBy: {
            popularity: "desc",
          },
        }),
        // Documentary shows (genre id 99)
        prisma.show.findMany({
          where: {
            mediaType: mediaType as MEDIA_TYPE,
            genres: {
              some: {
                genreId: 99,
              },
            },
          },
          include: {
            genres: {
              include: {
                genre: true,
              },
            },
            videos: {
              where: {
                type: "Trailer",
              },
              take: 1,
            },
          },
          take: 20,
          orderBy: {
            popularity: "desc",
          },
        }),
      ])

      // Transform the data to match the expected format
      const transformShow = (show: any) => ({
        showId: show.id,
        id: show.tmdbId || 0,
        title: show.title,
        name: show.name,
        original_title: show.originalTitle,
        poster_path: show.posterPath,
        backdrop_path: show.backdropPath,
        overview: show.overview,
        original_language: show.originalLanguage,
        media_type: show.mediaType,
        status: show.status,
        tagline: show.tagline,
        budget: show.budget,
        homepage: show.homepage,
        imdb_id: show.imdbId,
        popularity: show.popularity,
        vote_average: show.voteAverage,
        vote_count: show.voteCount,
        release_date: show.releaseDate,
        first_air_date: show.firstAirDate,
        last_air_date: show.lastAirDate,
        number_of_seasons: show.numberOfSeasons,
        number_of_episodes: show.numberOfEpisodes,
        revenue: show.revenue,
        runtime: show.runtime,
        adult: show.adult,
        video: show.video,
      })

      return {
        trending: trending.map(transformShow),
        topRated: topRated.map(transformShow),
        netflix: netflix.map(transformShow),
        action: action.map(transformShow),
        comedy: comedy.map(transformShow),
        horror: horror.map(transformShow),
        romance: romance.map(transformShow),
        docs: docs.map(transformShow),
      }
    }),

  // Get new and popular shows
  getNewAndPopularShows: publicProcedure
    .query(async () => {
      const [popularTvs, popularMovies, trendingTvs, trendingMovies] = await Promise.all([
        // Popular TV shows
        prisma.show.findMany({
          where: {
            mediaType: "tv",
          },
          include: {
            genres: {
              include: {
                genre: true,
              },
            },
            videos: {
              where: {
                type: "Trailer",
              },
              take: 1,
            },
          },
          take: 20,
          orderBy: {
            popularity: "desc",
          },
        }),
        // Popular movies
        prisma.show.findMany({
          where: {
            mediaType: "movie",
          },
          include: {
            genres: {
              include: {
                genre: true,
              },
            },
            videos: {
              where: {
                type: "Trailer",
              },
              take: 1,
            },
          },
          take: 20,
          orderBy: {
            popularity: "desc",
          },
        }),
        // Trending TV shows
        prisma.show.findMany({
          where: {
            mediaType: "tv",
            isTrending: true,
          },
          include: {
            genres: {
              include: {
                genre: true,
              },
            },
            videos: {
              where: {
                type: "Trailer",
              },
              take: 1,
            },
          },
          take: 20,
          orderBy: {
            popularity: "desc",
          },
        }),
        // Trending movies
        prisma.show.findMany({
          where: {
            mediaType: "movie",
            isTrending: true,
          },
          include: {
            genres: {
              include: {
                genre: true,
              },
            },
            videos: {
              where: {
                type: "Trailer",
              },
              take: 1,
            },
          },
          take: 20,
          orderBy: {
            popularity: "desc",
          },
        }),
      ])

      // Transform the data to match the expected format
      const transformShow = (show: any) => ({
        showId: show.id,
        id: show.tmdbId || 0,
        title: show.title,
        name: show.name,
        original_title: show.originalTitle,
        poster_path: show.posterPath,
        backdrop_path: show.backdropPath,
        overview: show.overview,
        original_language: show.originalLanguage,
        media_type: show.mediaType,
        status: show.status,
        tagline: show.tagline,
        budget: show.budget,
        homepage: show.homepage,
        imdb_id: show.imdbId,
        popularity: show.popularity,
        vote_average: show.voteAverage,
        vote_count: show.voteCount,
        release_date: show.releaseDate,
        first_air_date: show.firstAirDate,
        last_air_date: show.lastAirDate,
        number_of_seasons: show.numberOfSeasons,
        number_of_episodes: show.numberOfEpisodes,
        revenue: show.revenue,
        runtime: show.runtime,
        adult: show.adult,
        video: show.video,
      })

      return {
        popularTvs: popularTvs.map(transformShow),
        popularMovies: popularMovies.map(transformShow),
        trendingTvs: trendingTvs.map(transformShow),
        trendingMovies: trendingMovies.map(transformShow),
      }
    }),

  // Search shows
  searchShows: publicProcedure
    .input(z.object({
      query: z.string().min(1),
    }))
    .query(async ({ input }) => {
      const { query } = input

      const shows = await prisma.show.findMany({
        where: {
          OR: [
            {
              title: {
                contains: query,
                mode: "insensitive",
              },
            },
            {
              name: {
                contains: query,
                mode: "insensitive",
              },
            },
            {
              overview: {
                contains: query,
                mode: "insensitive",
              },
            },
          ],
        },
        include: {
          genres: {
            include: {
              genre: true,
            },
          },
          videos: {
            where: {
              type: "Trailer",
            },
            take: 1,
          },
        },
        take: 50,
        orderBy: {
          popularity: "desc",
        },
      })

      // Transform the data to match the expected format
      const transformShow = (show: any) => ({
        showId: show.id,
        id: show.tmdbId || 0,
        title: show.title,
        name: show.name,
        original_title: show.originalTitle,
        poster_path: show.posterPath,
        backdrop_path: show.backdropPath,
        overview: show.overview,
        original_language: show.originalLanguage,
        media_type: show.mediaType,
        status: show.status,
        tagline: show.tagline,
        budget: show.budget,
        homepage: show.homepage,
        imdb_id: show.imdbId,
        popularity: show.popularity,
        vote_average: show.voteAverage,
        vote_count: show.voteCount,
        release_date: show.releaseDate,
        first_air_date: show.firstAirDate,
        last_air_date: show.lastAirDate,
        number_of_seasons: show.numberOfSeasons,
        number_of_episodes: show.numberOfEpisodes,
        revenue: show.revenue,
        runtime: show.runtime,
        adult: show.adult,
        video: show.video,
      })

      return {
        results: shows.map(transformShow),
      }
    }),

  // Get show details with genres and videos
  getShowDetails: publicProcedure
    .input(z.object({
      showId: z.string().optional(),
      tmdbId: z.number().optional(),
    }))
    .query(async ({ input }) => {
      const { showId, tmdbId } = input

      if (!showId && !tmdbId) {
        throw new Error("Either showId or tmdbId must be provided")
      }

      const show = await prisma.show.findFirst({
        where: showId ? { id: showId } : { tmdbId: tmdbId },
        include: {
          genres: {
            include: {
              genre: true,
            },
          },
          videos: true,
        },
      })

      if (!show) {
        throw new Error("Show not found")
      }

      // Transform the data to match the expected format
      return {
        showId: show.id,
        id: show.tmdbId || 0,
        title: show.title,
        name: show.name,
        original_title: show.originalTitle,
        poster_path: show.posterPath,
        backdrop_path: show.backdropPath,
        overview: show.overview,
        original_language: show.originalLanguage,
        media_type: show.mediaType,
        status: show.status,
        tagline: show.tagline,
        budget: show.budget,
        homepage: show.homepage,
        imdb_id: show.imdbId,
        popularity: show.popularity,
        vote_average: show.voteAverage,
        vote_count: show.voteCount,
        release_date: show.releaseDate,
        first_air_date: show.firstAirDate,
        last_air_date: show.lastAirDate,
        number_of_seasons: show.numberOfSeasons,
        number_of_episodes: show.numberOfEpisodes,
        revenue: show.revenue,
        runtime: show.runtime,
        adult: show.adult,
        video: show.video,
        genres: show.genres.map((sg) => ({
          id: sg.genre.id,
          name: sg.genre.name,
        })),
        videos: {
          results: show.videos.map((video) => ({
            iso_639_1: video.iso6391,
            iso_3166_1: video.iso31661,
            name: video.name,
            key: video.key,
            site: video.site,
            size: video.size || 1080,
            type: video.type.replace("_", " ") as any,
            official: video.official,
            published_at: video.publishedAt,
            id: video.id,
          })),
        },
      }
    }),
})

# Since the ".env" file is gitignored, you can use the ".env.example" file to
# build a new ".env" file when you clone the repo. Keep this file up-to-date
# when you add new variables to `.env`.

# This file will be committed to version control, so make sure not to have any
# secrets in it. If you are cloning this repo, create a copy of this file named
# ".env" and populate it with your secrets.

# When adding additional environment variables, the schema in "/src/env.mjs"
# should be updated accordingly.

# App
NEXT_PUBLIC_APP_URL="http://localhost:3000"

# Prisma
# https://www.prisma.io/docs/reference/database-reference/connection-urls#env
DATABASE_URL="file:./db.sqlite"

# Next Auth
# You can generate a new secret on the command line with:
# openssl rand -base64 32
# https://next-auth.js.org/configuration/options#secret
# NEXTAUTH_SECRET=""
NEXTAUTH_URL="http://localhost:3000"


# Next Auth Google Provider
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

# TMDB
NEXT_PUBLIC_TMDB_API_KEY=

# Stripe
# Stripe Secret Key found at https://dashboard.stripe.com/test/apikeys
STRIPE_API_KEY=
# Stripe Webhook Secret found at https://dashboard.stripe.com/test/webhooks/create?endpoint_location=local
STRIPE_WEBHOOK_SECRET=
# Stripe Product and Price IDs for your created products 
# found at https://dashboard.stripe.com/test/products
STRIPE_MOBILE_PRICE_ID=
STRIPE_BASIC_PRICE_ID=
STRIPE_STANDARD_PRICE_ID=
STRIPE_PREMIUM_PRICE_ID=

-- Create<PERSON><PERSON>
CREATE TYPE "MEDIA_TYPE" AS ENUM ('movie', 'tv');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "VIDEO_TYPE" AS ENUM ('Bloopers', 'Featurette', 'Behind_the_Scenes', 'Clip', 'Trailer', 'Teaser', 'Full_Movie', 'Episode');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "LANGUAGE" AS ENUM ('BAHASA_INDONESIA', 'BAHASA_MELAYU', 'DANSK', 'DEUTSCH', 'ENGLISH', 'ESPANOL', 'FILIPINO', 'FRANCAIS', 'HRVATSKI', 'ITALIANO', 'HINDI', 'BANGLA', 'MAGY<PERSON>', 'NEDERLANDS', 'NORSK_BOKMAL', 'POLSKI', 'PORTUGUES', 'ROMANA', 'SUOMI', 'SVENSKA', 'TIENG_VIET', 'TURKCE', 'CESTINA');

-- CreateTable
CREATE TABLE "Account" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "providerAccountId" TEXT NOT NULL,
    "refresh_token" TEXT,
    "access_token" TEXT,
    "expires_at" INTEGER,
    "token_type" TEXT,
    "scope" TEXT,
    "id_token" TEXT,
    "session_state" TEXT,

    CONSTRAINT "Account_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Session" (
    "id" TEXT NOT NULL,
    "sessionToken" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Session_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "User" (
    "id" TEXT NOT NULL,
    "name" TEXT,
    "email" TEXT,
    "emailVerified" TIMESTAMP(3),
    "image" TEXT,
    "phoneNumber" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "stripe_customer_id" TEXT,
    "stripe_subscription_id" TEXT,
    "stripe_price_id" TEXT,
    "stripe_current_period_end" TIMESTAMP(3),

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "VerificationToken" (
    "identifier" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL
);

-- CreateTable
CREATE TABLE "Profile" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "email" TEXT,
    "pin" INTEGER,
    "language" "LANGUAGE" NOT NULL DEFAULT 'ENGLISH',
    "gameHandle" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "iconId" TEXT NOT NULL,

    CONSTRAINT "Profile_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Icon" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "href" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Icon_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MyShow" (
    "id" TEXT NOT NULL,
    "show_id" TEXT NOT NULL,
    "profile_id" TEXT NOT NULL,
    "added_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "MyShow_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Show" (
    "id" TEXT NOT NULL,
    "tmdbId" INTEGER,
    "title" TEXT,
    "name" TEXT,
    "original_title" TEXT,
    "original_language" TEXT NOT NULL,
    "overview" TEXT,
    "poster_path" TEXT,
    "backdrop_path" TEXT,
    "media_type" "MEDIA_TYPE" NOT NULL,
    "adult" BOOLEAN NOT NULL DEFAULT false,
    "video" BOOLEAN NOT NULL DEFAULT false,
    "popularity" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "vote_average" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "vote_count" INTEGER NOT NULL DEFAULT 0,
    "release_date" TEXT,
    "first_air_date" TEXT,
    "last_air_date" TEXT,
    "number_of_seasons" INTEGER,
    "number_of_episodes" INTEGER,
    "runtime" INTEGER,
    "status" TEXT,
    "tagline" TEXT,
    "budget" INTEGER,
    "revenue" INTEGER,
    "homepage" TEXT,
    "imdb_id" TEXT,
    "is_trending" BOOLEAN NOT NULL DEFAULT false,
    "is_top_rated" BOOLEAN NOT NULL DEFAULT false,
    "is_netflix" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Show_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Genre" (
    "id" INTEGER NOT NULL,
    "name" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Genre_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ShowGenre" (
    "showId" TEXT NOT NULL,
    "genreId" INTEGER NOT NULL,

    CONSTRAINT "ShowGenre_pkey" PRIMARY KEY ("showId","genreId")
);

-- CreateTable
CREATE TABLE "Video" (
    "id" TEXT NOT NULL,
    "show_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "site" TEXT NOT NULL DEFAULT 'YouTube',
    "size" INTEGER,
    "type" "VIDEO_TYPE" NOT NULL,
    "official" BOOLEAN NOT NULL DEFAULT false,
    "published_at" TEXT NOT NULL,
    "iso_639_1" TEXT NOT NULL DEFAULT 'en',
    "iso_3166_1" TEXT NOT NULL DEFAULT 'US',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Video_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Account_provider_providerAccountId_key" ON "Account"("provider", "providerAccountId");

-- CreateIndex
CREATE UNIQUE INDEX "Session_sessionToken_key" ON "Session"("sessionToken");

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");

-- CreateIndex
CREATE UNIQUE INDEX "User_phoneNumber_key" ON "User"("phoneNumber");

-- CreateIndex
CREATE UNIQUE INDEX "User_stripe_customer_id_key" ON "User"("stripe_customer_id");

-- CreateIndex
CREATE UNIQUE INDEX "User_stripe_subscription_id_key" ON "User"("stripe_subscription_id");

-- CreateIndex
CREATE UNIQUE INDEX "VerificationToken_token_key" ON "VerificationToken"("token");

-- CreateIndex
CREATE UNIQUE INDEX "VerificationToken_identifier_token_key" ON "VerificationToken"("identifier", "token");

-- CreateIndex
CREATE INDEX "MyShow_profile_id_idx" ON "MyShow"("profile_id");

-- CreateIndex
CREATE INDEX "MyShow_show_id_idx" ON "MyShow"("show_id");

-- CreateIndex
CREATE UNIQUE INDEX "MyShow_show_id_profile_id_key" ON "MyShow"("show_id", "profile_id");

-- CreateIndex
CREATE UNIQUE INDEX "Show_tmdbId_key" ON "Show"("tmdbId");

-- CreateIndex
CREATE INDEX "Show_media_type_idx" ON "Show"("media_type");

-- CreateIndex
CREATE INDEX "Show_popularity_idx" ON "Show"("popularity");

-- CreateIndex
CREATE INDEX "Show_vote_average_idx" ON "Show"("vote_average");

-- CreateIndex
CREATE INDEX "Show_is_trending_idx" ON "Show"("is_trending");

-- CreateIndex
CREATE INDEX "Show_is_top_rated_idx" ON "Show"("is_top_rated");

-- CreateIndex
CREATE INDEX "Show_is_netflix_idx" ON "Show"("is_netflix");

-- CreateIndex
CREATE UNIQUE INDEX "Genre_id_key" ON "Genre"("id");

-- CreateIndex
CREATE INDEX "ShowGenre_showId_idx" ON "ShowGenre"("showId");

-- CreateIndex
CREATE INDEX "ShowGenre_genreId_idx" ON "ShowGenre"("genreId");

-- CreateIndex
CREATE INDEX "Video_show_id_idx" ON "Video"("show_id");

-- CreateIndex
CREATE INDEX "Video_type_idx" ON "Video"("type");

-- AddForeignKey
ALTER TABLE "Account" ADD CONSTRAINT "Account_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Session" ADD CONSTRAINT "Session_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Profile" ADD CONSTRAINT "Profile_iconId_fkey" FOREIGN KEY ("iconId") REFERENCES "Icon"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Profile" ADD CONSTRAINT "Profile_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MyShow" ADD CONSTRAINT "MyShow_show_id_fkey" FOREIGN KEY ("show_id") REFERENCES "Show"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MyShow" ADD CONSTRAINT "MyShow_profile_id_fkey" FOREIGN KEY ("profile_id") REFERENCES "Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ShowGenre" ADD CONSTRAINT "ShowGenre_showId_fkey" FOREIGN KEY ("showId") REFERENCES "Show"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ShowGenre" ADD CONSTRAINT "ShowGenre_genreId_fkey" FOREIGN KEY ("genreId") REFERENCES "Genre"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Video" ADD CONSTRAINT "Video_show_id_fkey" FOREIGN KEY ("show_id") REFERENCES "Show"("id") ON DELETE CASCADE ON UPDATE CASCADE;

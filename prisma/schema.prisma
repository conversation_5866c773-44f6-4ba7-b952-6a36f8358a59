generator client {
    provider = "prisma-client-js"
}

datasource db {
    provider = "postgresql"
    url      = env("DATABASE_URL")
}

// Necessary for Next auth
model Account {
    id                String  @id @default(cuid())
    userId            String
    type              String
    provider          String
    providerAccountId String
    refresh_token     String?
    access_token      String?
    expires_at        Int?
    token_type        String?
    scope             String?
    id_token          String?
    session_state     String?
    user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

    @@unique([provider, providerAccountId])
}

model Session {
    id           String   @id @default(cuid())
    sessionToken String   @unique
    userId       String
    expires      DateTime
    user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
    id            String    @id @default(cuid())
    name          String?
    email         String?   @unique
    emailVerified DateTime?
    image         String?
    phoneNumber   String?   @unique
    createdAt     DateTime  @default(now()) @map(name: "created_at")
    updatedAt     DateTime  @default(now()) @map(name: "updated_at")

    accounts Account[]
    sessions Session[]
    profiles Profile[]

    stripeCustomerId       String?   @unique @map(name: "stripe_customer_id")
    stripeSubscriptionId   String?   @unique @map(name: "stripe_subscription_id")
    stripePriceId          String?   @map(name: "stripe_price_id")
    stripeCurrentPeriodEnd DateTime? @map(name: "stripe_current_period_end")
}

model VerificationToken {
    identifier String
    token      String   @unique
    expires    DateTime

    @@unique([identifier, token])
}

model Profile {
    id         String   @id @default(cuid())
    userId     String
    name       String
    email      String?
    pin        Int?
    icon       Icon     @relation(fields: [iconId], references: [id], onDelete: Cascade)
    language   LANGUAGE @default(ENGLISH)
    gameHandle String?
    user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)
    createdAt  DateTime @default(now())
    updatedAt  DateTime @updatedAt
    iconId     String
    myList     MyShow[]
}

model Icon {
    id        String    @id @default(cuid())
    title     String
    href      String
    createdAt DateTime  @default(now())
    updatedAt DateTime  @updatedAt
    profiles  Profile[]
}

model MyShow {
    showId             String     @id @default(cuid())
    id                 Int // tmdbId
    name               String?
    title              String?
    original_title     String?
    poster_path        String?
    backdrop_path      String?
    overview           String?
    original_language  String
    media_type         MEDIA_TYPE
    status             String?
    tagline            String?
    budget             Int?
    homepage           String?
    imdb_id            String?
    popularity         Float
    vote_average       Float
    vote_count         Int
    release_date       String?
    first_air_date     String?
    last_air_date      String?
    number_of_seasons  Int?
    number_of_episodes Int?
    revenue            Int?
    runtime            Int?
    adult              Boolean    @default(false)
    video              Boolean    @default(false)
    profile            Profile?   @relation(fields: [profileId], references: [id])
    profileId          String?
}

enum MEDIA_TYPE {
    movie
    tv
}

// New models for shows/movies database
model Show {
    id                 String      @id @default(cuid())
    tmdbId             Int?        @unique // Keep TMDB ID for reference
    title              String?
    name               String?     // For TV shows
    originalTitle      String?     @map("original_title")
    originalLanguage   String      @map("original_language")
    overview           String?     @db.Text
    posterPath         String?     @map("poster_path")
    backdropPath       String?     @map("backdrop_path")
    mediaType          MEDIA_TYPE  @map("media_type")
    adult              Boolean     @default(false)
    video              Boolean     @default(false)
    popularity         Float       @default(0)
    voteAverage        Float       @map("vote_average") @default(0)
    voteCount          Int         @map("vote_count") @default(0)
    releaseDate        String?     @map("release_date")
    firstAirDate       String?     @map("first_air_date")
    lastAirDate        String?     @map("last_air_date")
    numberOfSeasons    Int?        @map("number_of_seasons")
    numberOfEpisodes   Int?        @map("number_of_episodes")
    runtime            Int?
    status             String?
    tagline            String?
    budget             Int?
    revenue            Int?
    homepage           String?
    imdbId             String?     @map("imdb_id")

    // Streaming and categorization
    isTrending         Boolean     @default(false) @map("is_trending")
    isTopRated         Boolean     @default(false) @map("is_top_rated")
    isNetflix          Boolean     @default(false) @map("is_netflix")

    createdAt          DateTime    @default(now()) @map("created_at")
    updatedAt          DateTime    @updatedAt @map("updated_at")

    // Relations
    genres             ShowGenre[]
    videos             Video[]
    myShows            MyShow[]

    @@index([mediaType])
    @@index([popularity])
    @@index([voteAverage])
    @@index([isTrending])
    @@index([isTopRated])
    @@index([isNetflix])
}

model Genre {
    id          Int         @id
    name        String
    createdAt   DateTime    @default(now()) @map("created_at")
    updatedAt   DateTime    @updatedAt @map("updated_at")

    // Relations
    shows       ShowGenre[]

    @@unique([id])
}

model ShowGenre {
    showId      String
    genreId     Int
    show        Show    @relation(fields: [showId], references: [id], onDelete: Cascade)
    genre       Genre   @relation(fields: [genreId], references: [id], onDelete: Cascade)

    @@id([showId, genreId])
    @@index([showId])
    @@index([genreId])
}

model Video {
    id              String      @id @default(cuid())
    showId          String      @map("show_id")
    name            String
    key             String      // YouTube key or m3u8 URL
    site            String      @default("YouTube") // YouTube, m3u8, etc.
    size            Int?
    type            VIDEO_TYPE
    official        Boolean     @default(false)
    publishedAt     String      @map("published_at")
    iso6391         String      @map("iso_639_1") @default("en")
    iso31661        String      @map("iso_3166_1") @default("US")

    createdAt       DateTime    @default(now()) @map("created_at")
    updatedAt       DateTime    @updatedAt @map("updated_at")

    // Relations
    show            Show        @relation(fields: [showId], references: [id], onDelete: Cascade)

    @@index([showId])
    @@index([type])
}

enum VIDEO_TYPE {
    Bloopers
    Featurette
    Behind_the_Scenes
    Clip
    Trailer
    Teaser
    Full_Movie
    Episode
}

enum LANGUAGE {
    BAHASA_INDONESIA
    BAHASA_MELAYU
    DANSK
    DEUTSCH
    ENGLISH
    ESPANOL
    FILIPINO
    FRANCAIS
    HRVATSKI
    ITALIANO
    HINDI
    BANGLA
    MAGYAR
    NEDERLANDS
    NORSK_BOKMAL
    POLSKI
    PORTUGUES
    ROMANA
    SUOMI
    SVENSKA
    TIENG_VIET
    TURKCE
    CESTINA
}
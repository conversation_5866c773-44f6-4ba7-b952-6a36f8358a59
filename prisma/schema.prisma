generator client {
    provider = "prisma-client-js"
}

datasource db {
    provider     = "mysql"
    url          = env("DATABASE_URL")
    relationMode = "prisma"
}

// Necessary for Next auth
model Account {
    id                String  @id @default(cuid())
    userId            String
    type              String
    provider          String
    providerAccountId String
    refresh_token     String? @db.Text
    access_token      String? @db.Text
    expires_at        Int?
    token_type        String?
    scope             String?
    id_token          String? @db.Text
    session_state     String?
    user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

    @@unique([provider, providerAccountId])
    @@index([userId])
}

model Session {
    id           String   @id @default(cuid())
    sessionToken String   @unique
    userId       String
    expires      DateTime
    user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

    @@index([userId])
}

model User {
    id            String    @id @default(cuid())
    name          String?
    email         String?   @unique
    emailVerified DateTime?
    image         String?
    phoneNumber   String?   @unique
    createdAt     DateTime  @default(now()) @map(name: "created_at")
    updatedAt     DateTime  @default(now()) @map(name: "updated_at")

    accounts Account[]
    sessions Session[]
    profiles Profile[]

    stripeCustomerId       String?   @unique @map(name: "stripe_customer_id")
    stripeSubscriptionId   String?   @unique @map(name: "stripe_subscription_id")
    stripePriceId          String?   @map(name: "stripe_price_id")
    stripeCurrentPeriodEnd DateTime? @map(name: "stripe_current_period_end")
}

model VerificationToken {
    identifier String
    token      String   @unique
    expires    DateTime

    @@unique([identifier, token])
}

model Profile {
    id         String   @id @default(cuid())
    userId     String
    name       String
    email      String?
    pin        Int?
    icon       Icon     @relation(fields: [iconId], references: [id], onDelete: Cascade)
    language   LANGUAGE @default(ENGLISH)
    gameHandle String?
    user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)
    createdAt  DateTime @default(now())
    updatedAt  DateTime @updatedAt
    iconId     String
    myList     MyShow[]

    @@index([userId])
    @@index([iconId])
}

model Icon {
    id        String    @id @default(cuid())
    title     String
    href      String
    createdAt DateTime  @default(now())
    updatedAt DateTime  @updatedAt
    profiles  Profile[]
}

model MyShow {
    showId             String     @id @default(cuid())
    id                 Int // tmdbId
    name               String?
    title              String?
    original_title     String?
    poster_path        String?
    backdrop_path      String?
    overview           String?    @db.Text
    original_language  String
    media_type         MEDIA_TYPE
    status             String?
    tagline            String?
    budget             Int?
    homepage           String?
    imdb_id            String?
    popularity         Float
    vote_average       Float
    vote_count         Int
    release_date       String?
    first_air_date     String?
    last_air_date      String?
    number_of_seasons  Int?
    number_of_episodes Int?
    revenue            Int?
    runtime            Int?
    adult              Boolean    @default(false)
    video              Boolean    @default(false)
    profile            Profile?   @relation(fields: [profileId], references: [id])
    profileId          String?

    @@index([profileId])
}

enum MEDIA_TYPE {
    movie
    tv
}

enum LANGUAGE {
    BAHASA_INDONESIA
    BAHASA_MELAYU
    DANSK
    DEUTSCH
    ENGLISH
    ESPANOL
    FILIPINO
    FRANCAIS
    HRVATSKI
    ITALIANO
    HINDI
    BANGLA
    MAGYAR
    NEDERLANDS
    NORSK_BOKMAL
    POLSKI
    PORTUGUES
    ROMANA
    SUOMI
    SVENSKA
    TIENG_VIET
    TURKCE
    CESTINA
}

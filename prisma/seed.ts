import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create genres first
  const genres = [
    { id: 28, name: 'Action' },
    { id: 12, name: 'Adventure' },
    { id: 16, name: 'Animation' },
    { id: 35, name: 'Comedy' },
    { id: 80, name: 'Crime' },
    { id: 99, name: 'Documentary' },
    { id: 18, name: 'Drama' },
    { id: 10751, name: 'Family' },
    { id: 14, name: 'Fantasy' },
    { id: 36, name: 'History' },
    { id: 27, name: 'Horror' },
    { id: 10402, name: 'Music' },
    { id: 9648, name: 'Mystery' },
    { id: 10749, name: 'Romance' },
    { id: 878, name: 'Science Fiction' },
    { id: 10770, name: 'TV Movie' },
    { id: 53, name: 'Thriller' },
    { id: 10752, name: 'War' },
    { id: 37, name: 'Western' },
  ]

  console.log('Creating genres...')
  for (const genre of genres) {
    await prisma.genre.upsert({
      where: { id: genre.id },
      update: {},
      create: genre,
    })
  }

  // Sample movies data
  const movies = [
    {
      tmdbId: 550,
      title: 'Fight Club',
      originalTitle: 'Fight Club',
      originalLanguage: 'en',
      overview: 'A ticking-time-bomb insomniac and a slippery soap salesman channel primal male aggression into a shocking new form of therapy.',
      posterPath: '/pB8BM7pdSp6B6Ih7QZ4DrQ3PmJK.jpg',
      backdropPath: '/fCayJrkfRaCRCTh8GqN30f8oyQF.jpg',
      mediaType: 'movie',
      adult: false,
      video: false,
      popularity: 61.416,
      voteAverage: 8.433,
      voteCount: 26280,
      releaseDate: '1999-10-15',
      runtime: 139,
      status: 'Released',
      tagline: 'Mischief. Mayhem. Soap.',
      budget: ********,
      revenue: *********,
      imdbId: 'tt0137523',
      isTrending: true,
      isTopRated: true,
      isNetflix: false,
      genreIds: [18, 53],
    },
    {
      tmdbId: 238,
      title: 'The Shawshank Redemption',
      originalTitle: 'The Shawshank Redemption',
      originalLanguage: 'en',
      overview: 'Framed in the 1940s for the double murder of his wife and her lover, upstanding banker Andy Dufresne begins a new life at the Shawshank prison.',
      posterPath: '/q6y0Go1tsGEsmtFryDOJo3dEmqu.jpg',
      backdropPath: '/zfbjgQE1uSd9wiPTX4VzsLi0rGG.jpg',
      mediaType: 'movie',
      adult: false,
      video: false,
      popularity: 88.22,
      voteAverage: 8.7,
      voteCount: 24500,
      releaseDate: '1994-09-23',
      runtime: 142,
      status: 'Released',
      tagline: 'Fear can hold you prisoner. Hope can set you free.',
      budget: ********,
      revenue: ********,
      imdbId: 'tt0111161',
      isTrending: false,
      isTopRated: true,
      isNetflix: true,
      genreIds: [18, 80],
    },
    {
      tmdbId: 680,
      title: 'Pulp Fiction',
      originalTitle: 'Pulp Fiction',
      originalLanguage: 'en',
      overview: 'A burger-loving hit man, his philosophical partner, a drug-addled gangster\'s moll and a washed-up boxer converge in this sprawling, comedic crime caper.',
      posterPath: '/d5iIlFn5s0ImszYzBPb8JPIfbXD.jpg',
      backdropPath: '/4cDFJr4HnXN5AdPw4AKrmLlMWdO.jpg',
      mediaType: 'movie',
      adult: false,
      video: false,
      popularity: 65.466,
      voteAverage: 8.5,
      voteCount: 25000,
      releaseDate: '1994-09-10',
      runtime: 154,
      status: 'Released',
      tagline: 'Just because you are a character doesn\'t mean you have character.',
      budget: 8000000,
      revenue: 214179088,
      imdbId: 'tt0110912',
      isTrending: true,
      isTopRated: true,
      isNetflix: false,
      genreIds: [80, 18],
    },
    {
      tmdbId: 155,
      title: 'The Dark Knight',
      originalTitle: 'The Dark Knight',
      originalLanguage: 'en',
      overview: 'Batman raises the stakes in his war on crime with the help of Lt. Jim Gordon and District Attorney Harvey Dent.',
      posterPath: '/qJ2tW6WMUDux911r6m7haRef0WH.jpg',
      backdropPath: '/hqkIcbrOHL86UncnHIsHVcVmzue.jpg',
      mediaType: 'movie',
      adult: false,
      video: false,
      popularity: 123.167,
      voteAverage: 8.5,
      voteCount: 31000,
      releaseDate: '2008-07-18',
      runtime: 152,
      status: 'Released',
      tagline: 'Welcome to a world without rules.',
      budget: 185000000,
      revenue: 1004558444,
      imdbId: 'tt0468569',
      isTrending: true,
      isTopRated: true,
      isNetflix: true,
      genreIds: [28, 80, 18],
    },
    {
      tmdbId: 13,
      title: 'Forrest Gump',
      originalTitle: 'Forrest Gump',
      originalLanguage: 'en',
      overview: 'A man with a low IQ has accomplished great things in his life and been present during significant historic events.',
      posterPath: '/arw2vcBveWOVZr6pxd9XTd1TdQa.jpg',
      backdropPath: '/3h1JZGDhZ8nzxdgvkxha0qBqi05.jpg',
      mediaType: 'movie',
      adult: false,
      video: false,
      popularity: 75.5,
      voteAverage: 8.5,
      voteCount: 24000,
      releaseDate: '1994-06-23',
      runtime: 142,
      status: 'Released',
      tagline: 'The world will never be the same once you\'ve seen it through the eyes of Forrest Gump.',
      budget: 55000000,
      revenue: 677387716,
      imdbId: 'tt0109830',
      isTrending: false,
      isTopRated: true,
      isNetflix: true,
      genreIds: [35, 18, 10749],
    },
    // Add more movies for different categories
    {
      tmdbId: 27205,
      title: 'Inception',
      originalTitle: 'Inception',
      originalLanguage: 'en',
      overview: 'Cobb, a skilled thief who commits corporate espionage by infiltrating the subconscious of his targets is offered a chance to regain his old life.',
      posterPath: '/9gk7adHYeDvHkCSEqAvQNLV5Uge.jpg',
      backdropPath: '/s3TBrRGB1iav7gFOCNx3H31MoES.jpg',
      mediaType: 'movie',
      adult: false,
      video: false,
      popularity: 147.5,
      voteAverage: 8.4,
      voteCount: 33000,
      releaseDate: '2010-07-16',
      runtime: 148,
      status: 'Released',
      tagline: 'Your mind is the scene of the crime.',
      budget: ********0,
      revenue: 836836967,
      imdbId: 'tt1375666',
      isTrending: true,
      isTopRated: true,
      isNetflix: false,
      genreIds: [28, 878, 53],
    },
    {
      tmdbId: 496243,
      title: 'Parasite',
      originalTitle: '기생충',
      originalLanguage: 'ko',
      overview: 'All unemployed, Ki-taek and his family take peculiar interest in the wealthy and glamorous Parks.',
      posterPath: '/7IiTTgloJzvGI1TAYymCfbfl3vT.jpg',
      backdropPath: '/TU9NIjwzjoKPwQHoHshkFcQUCG.jpg',
      mediaType: 'movie',
      adult: false,
      video: false,
      popularity: 98.3,
      voteAverage: 8.5,
      voteCount: 16500,
      releaseDate: '2019-05-30',
      runtime: 132,
      status: 'Released',
      tagline: 'Act like you own the place.',
      budget: 11400000,
      revenue: 258800000,
      imdbId: 'tt6751668',
      isTrending: true,
      isTopRated: true,
      isNetflix: true,
      genreIds: [35, 53, 18],
    },
    {
      tmdbId: 278,
      title: 'The Godfather',
      originalTitle: 'The Godfather',
      originalLanguage: 'en',
      overview: 'Spanning the years 1945 to 1955, a chronicle of the fictional Italian-American Corleone crime family.',
      posterPath: '/3bhkrj58Vtu7enYsRolD1fZdja1.jpg',
      backdropPath: '/tmU7GeKVybMWFButWEGl2M4GeiP.jpg',
      mediaType: 'movie',
      adult: false,
      video: false,
      popularity: 131.3,
      voteAverage: 8.7,
      voteCount: 18500,
      releaseDate: '1972-03-14',
      runtime: 175,
      status: 'Released',
      tagline: 'An offer you can\'t refuse.',
      budget: 6000000,
      revenue: 245066411,
      imdbId: 'tt0068646',
      isTrending: false,
      isTopRated: true,
      isNetflix: true,
      genreIds: [18, 80],
    },
    {
      tmdbId: 19404,
      title: 'Dilwale Dulhania Le Jayenge',
      originalTitle: 'दिलवाले दुल्हनिया ले जायेंगे',
      originalLanguage: 'hi',
      overview: 'Raj is a rich, carefree, happy-go-lucky second generation NRI. Simran is the daughter of Chaudhary Baldev Singh.',
      posterPath: '/2CAL2433ZeIihfX1Hb2139CX0pW.jpg',
      backdropPath: '/90ez6ArvpO8bvpyIngBuwXOqJm5.jpg',
      mediaType: 'movie',
      adult: false,
      video: false,
      popularity: 34.5,
      voteAverage: 8.7,
      voteCount: 4200,
      releaseDate: '1995-10-20',
      runtime: 190,
      status: 'Released',
      tagline: 'Come... Fall in Love.',
      budget: 4000000,
      revenue: 13500000,
      imdbId: 'tt0112870',
      isTrending: false,
      isTopRated: true,
      isNetflix: false,
      genreIds: [35, 18, 10749],
    },
    // Horror movies
    {
      tmdbId: 346,
      title: 'Seven',
      originalTitle: 'Se7en',
      originalLanguage: 'en',
      overview: 'Two homicide detectives are on a desperate hunt for a serial killer whose crimes are based on the "seven deadly sins".',
      posterPath: '/6yoghtyTpznpBik8EngEmJskVUO.jpg',
      backdropPath: '/ba3OvlQVYdeHE2QzMDdAtKKkgdL.jpg',
      mediaType: 'movie',
      adult: false,
      video: false,
      popularity: 89.7,
      voteAverage: 8.4,
      voteCount: 15000,
      releaseDate: '1995-09-22',
      runtime: 127,
      status: 'Released',
      tagline: 'Seven deadly sins. Seven ways to die.',
      budget: 33000000,
      revenue: 327311859,
      imdbId: 'tt0114369',
      isTrending: false,
      isTopRated: true,
      isNetflix: false,
      genreIds: [80, 18, 27],
    },
  ]

  // Add TV shows
  const tvShows = [
    {
      tmdbId: 1399,
      name: 'Game of Thrones',
      title: 'Game of Thrones',
      originalTitle: 'Game of Thrones',
      originalLanguage: 'en',
      overview: 'Seven noble families fight for control of the mythical land of Westeros.',
      posterPath: '/u3bZgnGQ9T01sWNhyveQz0wH0Hl.jpg',
      backdropPath: '/suopoADq0k8YZr4dQXcU6pToj6s.jpg',
      mediaType: 'tv',
      adult: false,
      video: false,
      popularity: 369.594,
      voteAverage: 8.3,
      voteCount: 11504,
      firstAirDate: '2011-04-17',
      lastAirDate: '2019-05-19',
      numberOfSeasons: 8,
      numberOfEpisodes: 73,
      status: 'Ended',
      tagline: 'Winter Is Coming',
      imdbId: 'tt0944947',
      isTrending: true,
      isTopRated: true,
      isNetflix: false,
      genreIds: [18, 14, 12],
    },
    {
      tmdbId: 1396,
      name: 'Breaking Bad',
      title: 'Breaking Bad',
      originalTitle: 'Breaking Bad',
      originalLanguage: 'en',
      overview: 'A high school chemistry teacher diagnosed with inoperable lung cancer turns to manufacturing and selling methamphetamine.',
      posterPath: '/ggFHVNu6YYI5L9pCfOacjizRGt.jpg',
      backdropPath: '/tsRy63Mu5cu8etL1X7ZLyf7UP1M.jpg',
      mediaType: 'tv',
      adult: false,
      video: false,
      popularity: 317.701,
      voteAverage: 8.9,
      voteCount: 9413,
      firstAirDate: '2008-01-20',
      lastAirDate: '2013-09-29',
      numberOfSeasons: 5,
      numberOfEpisodes: 62,
      status: 'Ended',
      tagline: 'Change the equation.',
      imdbId: 'tt0903747',
      isTrending: true,
      isTopRated: true,
      isNetflix: true,
      genreIds: [18, 80],
    },
  ]

  console.log('Creating movies...')
  for (const movieData of movies) {
    const { genreIds, ...movie } = movieData
    
    const createdMovie = await prisma.show.upsert({
      where: { tmdbId: movie.tmdbId },
      update: {},
      create: movie,
    })

    // Connect genres
    for (const genreId of genreIds) {
      await prisma.showGenre.upsert({
        where: {
          showId_genreId: {
            showId: createdMovie.id,
            genreId: genreId,
          },
        },
        update: {},
        create: {
          showId: createdMovie.id,
          genreId: genreId,
        },
      })
    }

    // Add sample videos
    await prisma.video.upsert({
      where: { id: `${createdMovie.id}-trailer` },
      update: {},
      create: {
        id: `${createdMovie.id}-trailer`,
        showId: createdMovie.id,
        name: `${movie.title} - Official Trailer`,
        key: 'dQw4w9WgXcQ', // Sample YouTube key
        site: 'YouTube',
        size: 1080,
        type: 'Trailer',
        official: true,
        publishedAt: '2023-01-01T00:00:00Z',
        iso6391: 'en',
        iso31661: 'US',
      },
    })

    // Add sample m3u8 video for streaming
    await prisma.video.upsert({
      where: { id: `${createdMovie.id}-stream` },
      update: {},
      create: {
        id: `${createdMovie.id}-stream`,
        showId: createdMovie.id,
        name: `${movie.title} - Full Movie`,
        key: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4', // Sample streaming URL
        site: 'm3u8',
        size: 1080,
        type: 'Full_Movie',
        official: true,
        publishedAt: '2023-01-01T00:00:00Z',
        iso6391: 'en',
        iso31661: 'US',
      },
    })
  }

  console.log('Creating TV shows...')
  for (const showData of tvShows) {
    const { genreIds, ...show } = showData

    const createdShow = await prisma.show.upsert({
      where: { tmdbId: show.tmdbId },
      update: {},
      create: show,
    })

    // Connect genres
    for (const genreId of genreIds) {
      await prisma.showGenre.upsert({
        where: {
          showId_genreId: {
            showId: createdShow.id,
            genreId: genreId,
          },
        },
        update: {},
        create: {
          showId: createdShow.id,
          genreId: genreId,
        },
      })
    }

    // Add sample videos
    await prisma.video.upsert({
      where: { id: `${createdShow.id}-trailer` },
      update: {},
      create: {
        id: `${createdShow.id}-trailer`,
        showId: createdShow.id,
        name: `${show.name} - Official Trailer`,
        key: 'dQw4w9WgXcQ', // Sample YouTube key
        site: 'YouTube',
        size: 1080,
        type: 'Trailer',
        official: true,
        publishedAt: '2023-01-01T00:00:00Z',
        iso6391: 'en',
        iso31661: 'US',
      },
    })

    // Add sample m3u8 video for streaming
    await prisma.video.upsert({
      where: { id: `${createdShow.id}-stream` },
      update: {},
      create: {
        id: `${createdShow.id}-stream`,
        showId: createdShow.id,
        name: `${show.name} - Episode 1`,
        key: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4', // Sample streaming URL
        site: 'm3u8',
        size: 1080,
        type: 'Episode',
        official: true,
        publishedAt: '2023-01-01T00:00:00Z',
        iso6391: 'en',
        iso31661: 'US',
      },
    })
  }

  console.log('✅ Database seeded successfully!')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })

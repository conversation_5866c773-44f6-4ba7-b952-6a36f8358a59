{"name": "netflx-web", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "postinstall": "prisma generate", "lint": "next lint", "lint:fix": "next lint --fix", "start": "next start", "stripe:listen": "stripe listen --forward-to localhost:3000/api/webhooks/stripe --latest"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^5.17.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@tanstack/react-query": "^5.51.23", "@trpc/client": "^11.0.0-rc.507", "@trpc/next": "^11.0.0-rc.507", "@trpc/react-query": "^11.0.0-rc.507", "@trpc/server": "^11.0.0-rc.507", "@vercel/analytics": "^1.3.1", "@vercel/og": "^0.6.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "csstype": "^3.1.3", "framer-motion": "^11.3.24", "lucide-react": "^0.429.0", "next": "^15.4.5", "next-auth": "^4.24.7", "react": "^19.0.0-rc-69d4b800-20241021", "react-dom": "^19.0.0-rc-69d4b800-20241021", "react-hook-form": "^7.52.2", "react-hot-toast": "^2.4.1", "react-pin-input": "^1.3.1", "react-player": "^2.16.0", "stripe": "^16.8.0", "superjson": "^2.2.1", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8", "zustand": "^4.5.4"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "^4.3.1", "@types/eslint": "^9.6.1", "@types/node": "^22.4.1", "@types/prettier": "^3.0.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^8.1.0", "@typescript-eslint/parser": "^8.1.0", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-config-next": "^15.4.5", "eslint-config-prettier": "^9.1.0", "eslint-plugin-tailwindcss": "^3.17.4", "postcss": "^8.4.41", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.6", "prisma": "^5.17.0", "tailwindcss": "^3.4.7", "typescript": "^5.5.4"}, "ct3aMetadata": {"initVersion": "7.10.3"}}